<html class="scroll-smooth" lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   Cosmic Creation Animation
  </title>
  <script src="https://cdn.tailwindcss.com">
  </script>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;600&amp;display=swap" rel="stylesheet"/>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet"/>
  <style>
   body {
      font-family: "Poppins", sans-serif;
      background-color: #0a0a23;
      color: #f9f7f1;
      overflow: hidden;
      margin: 0;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      perspective: 1500px;
      cursor: none;
    }

    /* Custom cursor for presentation mode */
    .presentation-cursor {
      position: fixed;
      width: 20px;
      height: 20px;
      background: radial-gradient(circle, #ffd700 0%, #ffec99 50%, transparent 70%);
      border-radius: 50%;
      pointer-events: none;
      z-index: 9999;
      transition: transform 0.1s ease;
      box-shadow: 0 0 20px #ffd700aa;
    }
    .container {
      position: relative;
      width: 100vw;
      height: 100vh;
      overflow: hidden;
      background: radial-gradient(
        ellipse at center,
        #0a0a23 0%,
        #000000 80%
      );
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .slide {
      position: absolute;
      width: 100vw;
      height: 100vh;
      top: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 1rem 2rem;
      text-align: center;
      opacity: 0;
      transform-style: preserve-3d;
      backface-visibility: hidden;
      color: #f9f7f1;
      user-select: none;
      transition: all 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    .slide.active {
      opacity: 1;
      z-index: 10;
      animation: slideInMagic 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }
    .slide.exiting {
      animation: slideOutMagic 1.2s cubic-bezier(0.55, 0.06, 0.68, 0.19) forwards;
    }
    .slide img {
      max-width: 320px;
      max-height: 320px;
      border-radius: 1.5rem;
      box-shadow: 0 0 30px #ffd700cc;
      margin-bottom: 1.5rem;
      object-fit: contain;
      filter: drop-shadow(0 0 10px #ffd700);
      animation: pulseLight 3s ease-in-out infinite, cosmicGlow 4s ease-in-out infinite;
      transition: transform 0.3s ease;
    }

    .slide img:hover {
      transform: scale(1.1) rotateY(5deg);
      animation-play-state: paused;
    }
    h1,
    h2,
    h3 {
      margin: 0;
      font-weight: 600;
      text-shadow: 0 0 10px #ffd700cc;
    }
    h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      color: #fff9d6;
    }
    h2 {
      font-size: 2.25rem;
      margin-bottom: 0.75rem;
      color: #ffec99;
    }
    h3 {
      font-size: 1.75rem;
      margin-bottom: 0.5rem;
      color: #ffec99;
    }
    p {
      font-size: 1.15rem;
      max-width: 600px;
      line-height: 1.5;
      color: #ffec99cc;
      text-shadow: 0 0 5px #ffd700aa;
      margin: 0 auto;
    }
    /* Enhanced Animations */
    @keyframes pulseLight {
      0%, 100% {
        filter: drop-shadow(0 0 15px #fff9d6) brightness(1);
        opacity: 0.85;
        transform: scale(1);
      }
      50% {
        filter: drop-shadow(0 0 35px #fff9d6) brightness(1.2);
        opacity: 1;
        transform: scale(1.05);
      }
    }

    @keyframes slideInMagic {
      0% {
        opacity: 0;
        transform: translateY(60px) rotateX(15deg) scale(0.9);
        filter: blur(10px);
      }
      50% {
        opacity: 0.7;
        transform: translateY(20px) rotateX(5deg) scale(0.95);
        filter: blur(3px);
      }
      100% {
        opacity: 1;
        transform: translateY(0) rotateX(0deg) scale(1);
        filter: blur(0px);
      }
    }

    @keyframes slideOutMagic {
      0% {
        opacity: 1;
        transform: translateY(0) rotateX(0deg) scale(1);
        filter: blur(0px);
      }
      100% {
        opacity: 0;
        transform: translateY(-40px) rotateX(-10deg) scale(0.95);
        filter: blur(5px);
      }
    }

    @keyframes cosmicGlow {
      0%, 100% {
        box-shadow: 0 0 20px #ffd700cc, 0 0 40px #ffd70066, 0 0 60px #ffd70033;
      }
      50% {
        box-shadow: 0 0 30px #ffd700ff, 0 0 60px #ffd700aa, 0 0 90px #ffd70066;
      }
    }
    /* Side by side images */
    .side-by-side {
      display: flex;
      gap: 2rem;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      margin-bottom: 1.5rem;
    }

    .side-by-side img {
      max-width: 280px;
      max-height: 280px;
    }

    /* Creation Chain Flow */
    .creation-chain {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 1rem;
      margin: 2rem auto;
      max-width: 90vw;
      flex-wrap: wrap;
      padding: 2rem;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 2rem;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 215, 0, 0.2);
    }

    .chain-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      min-width: 100px;
    }

    .chain-item img {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      object-fit: cover;
      margin-bottom: 0.5rem;
      box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
      animation: pulseLight 3s ease-in-out infinite;
      transition: transform 0.3s ease;
    }

    .chain-item img:hover {
      transform: scale(1.2);
    }

    .chain-item span {
      font-size: 0.9rem;
      color: #ffd700;
      font-weight: 600;
      text-shadow: 0 0 5px rgba(255, 215, 0, 0.8);
    }

    .chain-arrow {
      font-size: 2rem;
      color: #ffd700;
      text-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
      animation: pulseLight 2s ease-in-out infinite;
      margin: 0 0.5rem;
    }

    /* Spiral container */
    .spiral-container {
      position: relative;
      width: 480px;
      height: 480px;
      margin: 0 auto 2rem;
      animation: slowRotate 60s linear infinite;
      filter: drop-shadow(0 0 20px #ffd700cc);
    }
    @keyframes slowRotate {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    /* Spiral images */
    .spiral-img {
      position: absolute;
      border-radius: 9999px;
      box-shadow: 0 0 15px #ffd700cc;
      object-fit: contain;
      filter: drop-shadow(0 0 10px #ffd700);
      background: radial-gradient(circle, #ffd70044 0%, transparent 70%);
    }
    /* Positions for spiral elements */
    .spiral-nirakar {
      width: 96px;
      height: 96px;
      top: 8px;
      left: 50%;
      transform: translateX(-50%);
      animation: pulseLight 3s ease-in-out infinite;
    }
    .spiral-shivshakti {
      width: 72px;
      height: 72px;
      top: 120px;
      left: 25%;
      transform: translateX(-50%);
      animation: pulseLight 3.5s ease-in-out infinite;
    }
    .spiral-vishnu {
      width: 64px;
      height: 64px;
      top: 220px;
      right: 25%;
      animation: pulseLight 4s ease-in-out infinite;
    }
    .spiral-brahma {
      width: 56px;
      height: 56px;
      bottom: 160px;
      left: 33%;
      transform: translateX(-50%);
      animation: pulseLight 4.5s ease-in-out infinite;
    }
    .spiral-farishtas {
      width: 48px;
      height: 48px;
      bottom: 120px;
      right: 50%;
      transform: translateX(50%);
      animation: pulseLight 5s ease-in-out infinite;
    }
    .spiral-aatmas {
      width: 40px;
      height: 40px;
      bottom: 80px;
      left: 16%;
      transform: translateX(-50%);
      animation: pulseLight 5.5s ease-in-out infinite;
    }
    .spiral-sapta {
      width: 36px;
      height: 36px;
      bottom: 48px;
      right: 33%;
      animation: pulseLight 6s ease-in-out infinite;
    }
    .spiral-prajapati {
      width: 32px;
      height: 32px;
      bottom: 32px;
      left: 50%;
      transform: translateX(-50%);
      animation: pulseLight 6.5s ease-in-out infinite;
    }
    .spiral-devtas {
      width: 28px;
      height: 28px;
      bottom: 20px;
      right: 25%;
      animation: pulseLight 7s ease-in-out infinite;
    }
    .spiral-humans {
      width: 24px;
      height: 24px;
      bottom: 0;
      left: 33%;
      transform: translateX(-50%);
      animation: pulseLight 7.5s ease-in-out infinite;
    }
    /* Responsive */
    @media (max-width: 768px) {
      .creation-chain {
        flex-direction: column;
        gap: 1.5rem;
        padding: 1.5rem;
      }

      .chain-arrow {
        transform: rotate(90deg);
        font-size: 1.5rem;
        margin: 0;
      }

      .chain-item img {
        width: 60px;
        height: 60px;
      }

      .chain-item span {
        font-size: 0.8rem;
      }

      .side-by-side {
        flex-direction: column;
        gap: 1rem;
      }

      .side-by-side img {
        max-width: 250px;
        max-height: 250px;
      }

      .spiral-container {
        width: 320px;
        height: 320px;
      }
      .spiral-nirakar {
        width: 64px;
        height: 64px;
        top: 6px;
      }
      .spiral-shivshakti {
        width: 48px;
        height: 48px;
        top: 80px;
        left: 25%;
      }
      .spiral-vishnu {
        width: 48px;
        height: 48px;
        top: 140px;
        right: 25%;
      }
      .spiral-brahma {
        width: 40px;
        height: 40px;
        bottom: 100px;
        left: 33%;
      }
      .spiral-farishtas {
        width: 36px;
        height: 36px;
        bottom: 80px;
        right: 50%;
      }
      .spiral-aatmas {
        width: 32px;
        height: 32px;
        bottom: 56px;
        left: 16%;
      }
      .spiral-sapta {
        width: 28px;
        height: 28px;
        bottom: 32px;
        right: 33%;
      }
      .spiral-prajapati {
        width: 24px;
        height: 24px;
        bottom: 20px;
        left: 50%;
      }
      .spiral-devtas {
        width: 20px;
        height: 20px;
        bottom: 12px;
        right: 25%;
      }
      .spiral-humans {
        width: 18px;
        height: 18px;
        bottom: 0;
        left: 33%;
      }
    }

    /* Fullscreen Control */
    .fullscreen-btn {
      position: fixed;
      top: 30px;
      right: 30px;
      background: linear-gradient(135deg, #ffd700, #ffec99);
      border: none;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      color: #0a0a23;
      font-size: 18px;
      box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
      z-index: 1000;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 215, 0, 0.3);
    }

    .fullscreen-btn:hover {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
    }

    .fullscreen-btn:active {
      transform: scale(0.95);
    }

    .progress-bar {
      position: fixed;
      bottom: 0;
      left: 0;
      height: 4px;
      background: linear-gradient(90deg, #ffd700, #ffec99);
      transition: width 0.3s ease;
      z-index: 1000;
      box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    }

    .slide-counter {
      position: fixed;
      top: 30px;
      left: 30px;
      background: rgba(0, 0, 0, 0.7);
      padding: 10px 20px;
      border-radius: 25px;
      color: #ffd700;
      font-weight: 600;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 215, 0, 0.3);
      z-index: 1000;
    }

    .paused-indicator {
      display: none; /* Hidden - no paused indicator shown */
    }
  </style>
 </head>
 <body>
  <!-- Custom Cursor -->
  <div class="presentation-cursor" id="customCursor"></div>
  <main aria-atomic="true" aria-live="polite" class="container" role="main">
   <!-- Slides container -->
   <section class="relative w-full h-full" id="slides-wrapper">
    <!-- Slide 0: Infinite Void -->
    <article aria-label="Infinite cosmic void with subtle stars" class="slide active" data-index="0">
     <img alt="Vast infinite cosmic void with subtle twinkling stars scattered across a deep cosmic black and violet background" height="320" loading="eager" src="https://storage.googleapis.com/a1aa/image/85f21763-2b82-4d2f-5c95-b1a97ea12c43.jpg" style="filter: drop-shadow(0 0 10px #fff); border-radius: 50%;" width="320"/>
     <h1>
      Begin with a vast, infinite void — a cosmic silence.
     </h1>
    </article>
    <!-- Slide 1: Nirakar Shiva -->
    <article aria-label="Nirakar Shiva glowing divine light" class="slide" data-index="1">
     <img alt="Nirakar Shiva, a formless glowing divine light in golden-white luminosity, softly glowing and floating in timeless cosmic space" height="320" loading="lazy" src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/7b6eb67a-b345-49dc-b07d-ab822c3d8ff0.png" width="320"/>
     <h2>
      Nirakar Shiva
     </h2>
     <p>
      An eternal, boundless divine light appears — Nirakar Shiva, glowing softly in golden-white luminosity, formless yet deeply peaceful, floating in timeless space.
     </p>
    </article>
    <!-- Slide 2: Aakar Shiva and Shakti -->
    <article aria-label="Aakar Shiva and Shakti divine figures" class="slide" data-index="2">
     <div class="side-by-side">
      <img alt="Aakar Shiva, a radiant majestic figure formed from divine golden light, serene and majestic, standing tall with cosmic energy" height="280" loading="lazy" src="https://storage.googleapis.com/a1aa/image/30e6f437-e554-4d24-d7d2-b22761dcf87e.jpg" width="280"/>
      <img alt="Shakti, a glowing powerful feminine force of energy, radiant with golden and amber light, swirling and vibrant" height="280" loading="lazy" src="https://storage.googleapis.com/a1aa/image/fab9ceed-0949-4d29-506c-d002817e74cd.jpg" width="280"/>
     </div>
     <h2>
      Aakar Shiva &amp; Shakti
     </h2>
     <p>
      They unite in harmony — forming ShivShakti, a cosmic duality in one form, radiating both power and balance.
     </p>
    </article>
    <!-- Slide 3: ShivShakti -->
    <article aria-label="ShivShakti cosmic duality" class="slide" data-index="3">
     <img alt="ShivShakti, a cosmic duality figure combining Aakar Shiva and Shakti, radiating golden light and balance, majestic and serene" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/bbefb9e3-9c01-4d62-e3f8-28e8e44b74c9.jpg" width="320"/>
     <h2>
      ShivShakti — Cosmic Duality
     </h2>
     <p>
      Radiating both power and balance, the union of Shiva and Shakti manifests cosmic harmony.
     </p>
    </article>
    <!-- Slide 4: Vishnu -->
    <article aria-label="Vishnu resting on coiled serpent" class="slide" data-index="4">
     <img alt="Vishnu, celestial blue figure with peaceful smile, resting on a coiled serpent, symbolizing balance and sustenance, glowing softly" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/3ce2322c-faa0-4eb4-6520-c59ef988cfa7.jpg" width="320"/>
     <h2>
      Vishnu — Balance and Sustenance
     </h2>
     <p>
      With a divine glance, Vishnu creates Brahma, seated on a lotus blooming from his navel.
     </p>
    </article>
    <!-- Slide 5: Brahma -->
    <article aria-label="Brahma radiant being with four heads on lotus" class="slide" data-index="5">
     <img alt="Brahma, radiant being with four heads, seated on a blooming lotus flower, glowing with creative power and divine light" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/82a46071-de2c-4e1a-831d-666ac049e52a.jpg" width="320"/>
     <h2>
      Brahma — The Creator
     </h2>
     <p>
      Infused with creative power, Brahma begins his task by first creating Farishtas (angels) and high-quality Aatmas (souls), then manifests Prajapati, the powerful progenitor emitting divine knowledge and willpower.
     </p>
    </article>
    <!-- Slide 6: Farishtas and Aatmas -->
    <article aria-label="Farishtas angels and Aatmas souls descending" class="slide" data-index="6">
     <div class="side-by-side">
      <img alt="Farishtas, transparent glowing angels of light, hovering in the sky with soft blue and white luminescence" height="280" loading="lazy" src="https://storage.googleapis.com/a1aa/image/ebd0dd3c-2457-4c62-7d61-5c9757089404.jpg" width="280"/>
      <img alt="Aatmas, shining brilliant stars representing souls, descending gently into a divine realm with golden-white glow" height="280" loading="lazy" src="https://storage.googleapis.com/a1aa/image/eb6a5414-cb58-4a77-b64c-638f9e2dcd4b.jpg" width="280"/>
     </div>
     <h2>
      Farishtas &amp; Aatmas
     </h2>
     <p>
      Transparent, glowing angels and brilliant souls descending gently into the divine realm.
     </p>
    </article>
    <!-- Slide 7: Prajapati -->
    <article aria-label="Prajapati powerful progenitor" class="slide" data-index="7">
     <img alt="Prajapati, powerful progenitor emitting divine knowledge and willpower, glowing golden figure with radiant aura" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/42739393-4d1e-4015-a1d3-7d3910131fe9.jpg" width="320"/>
     <h2>
      Prajapati — Progenitor
     </h2>
     <p>
      Emitting divine knowledge and willpower, the powerful progenitor who continues creation.
     </p>
    </article>
    <!-- Slide 8: Sapta Rishis -->
    <article aria-label="Sapta Rishis seven glowing sages in meditation" class="slide" data-index="8">
     <div style="display:flex;gap:0.5rem;justify-content:center;flex-wrap:wrap;max-width:600px;margin-bottom:1rem;">
      <img alt="Sapta Rishi 1, glowing sage with pink aura, seated in deep meditation surrounded by celestial energy" height="80" loading="lazy" src="https://storage.googleapis.com/a1aa/image/c6df15fa-26e7-4f49-bfeb-4d8ca9967cf2.jpg" width="80"/>
      <img alt="Sapta Rishi 2, glowing sage with aqua aura, seated in deep meditation surrounded by celestial energy" height="80" loading="lazy" src="https://storage.googleapis.com/a1aa/image/7a43bc2d-818e-496c-9ea0-2182b7372f48.jpg" width="80"/>
      <img alt="Sapta Rishi 3, glowing sage with violet aura, seated in deep meditation surrounded by celestial energy" height="80" loading="lazy" src="https://storage.googleapis.com/a1aa/image/6abff991-8b26-41fa-b716-200812bacaa8.jpg" width="80"/>
      <img alt="Sapta Rishi 4, glowing sage with golden aura, seated in deep meditation surrounded by celestial energy" height="80" loading="lazy" src="https://storage.googleapis.com/a1aa/image/a56cb33c-4919-45a5-5c1a-e5de870afe53.jpg" width="80"/>
      <img alt="Sapta Rishi 5, glowing sage with sky blue aura, seated in deep meditation surrounded by celestial energy" height="80" loading="lazy" src="https://storage.googleapis.com/a1aa/image/ede0e13b-4678-45a8-b47c-2202d290ba7c.jpg" width="80"/>
      <img alt="Sapta Rishi 6, glowing sage with coral aura, seated in deep meditation surrounded by celestial energy" height="80" loading="lazy" src="https://storage.googleapis.com/a1aa/image/9efb9688-9093-414c-f979-6e15d36b7d76.jpg" width="80"/>
      <img alt="Sapta Rishi 7, glowing sage with light green aura, seated in deep meditation surrounded by celestial energy" height="80" loading="lazy" src="https://storage.googleapis.com/a1aa/image/5ff7a7de-eeaa-4bfa-b2d1-bde70d20c5d2.jpg" width="80"/>
     </div>
     <h2>
      Sapta Rishis — Seven Glowing Sages
     </h2>
     <p>
      Seven glowing sages, each with unique colored auras, seated in deep meditation surrounded by celestial energy.
     </p>
    </article>
    <!-- Slide 9: Devtas Slideshow -->
    <article aria-label="Devtas celestial beings representing elements" class="slide" data-index="9">
     <div aria-atomic="true" aria-live="polite" class="slideshow-container" role="region" style="max-width: 700px; margin: 0 auto;">
      <div class="slide-devtas active" data-index="0" style="text-align:center; color:#ffda44;">
       <img alt="Agni, Fire Deity, celestial being glowing with intense orange and red flames" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/8ea89d6d-06f9-4b50-1e62-96b3909a3ede.jpg" style="border-radius:1rem; box-shadow:0 0 20px #ff4500;" width="400"/>
       <h3>
        Agni (Fire)
       </h3>
       <p>
        Celestial being glowing with intense orange and red flames, representing the element of fire.
       </p>
      </div>
      <div class="slide-devtas" data-index="1" style="text-align:center; color:#a0d8ef;">
       <img alt="Vayu, Air Deity, celestial being glowing with soft white and grey swirling winds" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/b4ab0d83-de8a-4d97-d232-88c4deb19c32.jpg" style="border-radius:1rem; box-shadow:0 0 20px #a0d8ef;" width="400"/>
       <h3>
        Vayu (Air)
       </h3>
       <p>
        Celestial being glowing with soft white and grey swirling winds, representing the element of air.
       </p>
      </div>
      <div class="slide-devtas" data-index="2" style="text-align:center; color:#1e40af;">
       <img alt="Dyaus, Sky Deity, celestial being glowing with deep blue and cosmic sky hues" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/b05e3fc4-ab49-4352-d830-a141a6b80301.jpg" style="border-radius:1rem; box-shadow:0 0 20px #1e40af;" width="400"/>
       <h3>
        Dyaus (Sky)
       </h3>
       <p>
        Celestial being glowing with deep blue and cosmic sky hues, representing the element of sky.
       </p>
      </div>
      <div class="slide-devtas" data-index="3" style="text-align:center; color:#4d7c0f;">
       <img alt="Prithvi, Earth Deity, celestial being glowing with deep green and brown tones" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/2989d363-49de-4668-d0cd-c18dea894669.jpg" style="border-radius:1rem; box-shadow:0 0 20px #4d7c0f;" width="400"/>
       <h3>
        Prithvi (Earth)
       </h3>
       <p>
        Celestial being glowing with deep green and brown tones, representing the element of earth.
       </p>
      </div>
      <div class="slide-devtas" data-index="4" style="text-align:center; color:#2563eb;">
       <img alt="Varuna, Water Deity, celestial being glowing with deep blue and aqua waves" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/ed29aa2d-7608-4e94-458e-392c94970aec.jpg" style="border-radius:1rem; box-shadow:0 0 20px #2563eb;" width="400"/>
       <h3>
        Varuna (Water)
       </h3>
       <p>
        Celestial being glowing with deep blue and aqua waves, representing the element of water.
       </p>
      </div>
      <div class="slide-devtas" data-index="5" style="text-align:center; color:#fbbf24;">
       <img alt="Surya, Sun Deity, celestial being glowing with bright golden sun rays" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/738b1fdd-a09f-4c8e-0779-3734a2fc32dc.jpg" style="border-radius:1rem; box-shadow:0 0 20px #fbbf24;" width="400"/>
       <h3>
        Surya (Sun)
       </h3>
       <p>
        Celestial being glowing with bright golden sun rays, representing the sun.
       </p>
      </div>
      <div class="slide-devtas" data-index="6" style="text-align:center; color:#93c5fd;">
       <img alt="Chandra, Moon Deity, celestial being glowing with soft silver and blue moonlight" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/4fb2d617-82f2-47a3-84fd-d1211db5aaed.jpg" style="border-radius:1rem; box-shadow:0 0 20px #93c5fd;" width="400"/>
       <h3>
        Chandra (Moon)
       </h3>
       <p>
        Celestial being glowing with soft silver and blue moonlight, representing the moon.
       </p>
      </div>
      <div class="slide-devtas" data-index="7" style="text-align:center; color:#fcd34d;">
       <img alt="Nakshatra, Stars Deity, celestial being glowing with twinkling star lights" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/611f2a1e-a8a1-4f9d-7b3f-d3a62daa5a69.jpg" style="border-radius:1rem; box-shadow:0 0 20px #fcd34d;" width="400"/>
       <h3>
        Nakshatra (Stars)
       </h3>
       <p>
        Celestial being glowing with twinkling star lights, representing the stars.
       </p>
      </div>
     </div>
    </article>
    <!-- Slide 10: Humans -->
    <article aria-label="Humans placed gently on Earth in harmony with nature" class="slide" data-index="10">
     <img alt="Humans, fragile yet divine beings, placed gently on Earth surrounded by lush green nature, trees, rivers, and mountains, symbolizing harmony and conscious evolution" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/886eff4d-739f-46f3-73d9-17f664fb0a57.jpg" style="border-radius: 1.5rem; box-shadow: 0 0 30px #7cfc00cc;" width="320"/>
     <h2>
      Humans — Divine Beings Beginning Conscious Evolution
     </h2>
     <p>
      Fragile yet divine, placed gently on Earth in harmony with nature.
     </p>
    </article>
    <!-- Slide 11: Creation Chain Flow -->
    <article aria-label="Complete chain of creation from Nirakar Shiva to humans" class="slide" data-index="11">
     <h2 style="margin-bottom: 2rem;">
      The Complete Chain of Creation
     </h2>
     <div class="creation-chain">
      <div class="chain-item">
       <img alt="Nirakar Shiva glowing golden-white light" src="https://storage.googleapis.com/a1aa/image/0cbbcb32-d06c-4439-e6ac-2ca6de095853.jpg"/>
       <span>Nirakar Shiva</span>
      </div>
      <div class="chain-arrow">→</div>
      <div class="chain-item">
       <img alt="Aakar Shiva radiant majestic figure" src="https://storage.googleapis.com/a1aa/image/30e6f437-e554-4d24-d7d2-b22761dcf87e.jpg"/>
       <span>Aakar Shiva</span>
      </div>
      <div class="chain-arrow">→</div>
      <div class="chain-item">
       <img alt="Shakti powerful feminine force of energy" src="https://storage.googleapis.com/a1aa/image/fab9ceed-0949-4d29-506c-d002817e74cd.jpg"/>
       <span>Shakti</span>
      </div>
      <div class="chain-arrow">→</div>
      <div class="chain-item">
       <img alt="Vishnu celestial blue figure" src="https://storage.googleapis.com/a1aa/image/3ce2322c-faa0-4eb4-6520-c59ef988cfa7.jpg"/>
       <span>Vishnu</span>
      </div>
      <div class="chain-arrow">→</div>
      <div class="chain-item">
       <img alt="Brahma radiant being with four heads" src="https://storage.googleapis.com/a1aa/image/82a46071-de2c-4e1a-831d-666ac049e52a.jpg"/>
       <span>Brahma</span>
      </div>
      <div class="chain-arrow">→</div>
      <div class="chain-item">
       <img alt="Farishtas transparent glowing angels" src="https://storage.googleapis.com/a1aa/image/ebd0dd3c-2457-4c62-7d61-5c9757089404.jpg"/>
       <span>Farishtas</span>
      </div>
      <div class="chain-arrow">→</div>
      <div class="chain-item">
       <img alt="Devtas celestial beings representing elements" src="https://storage.googleapis.com/a1aa/image/8ea89d6d-06f9-4b50-1e62-96b3909a3ede.jpg"/>
       <span>Devtas</span>
      </div>
      <div class="chain-arrow">→</div>
      <div class="chain-item">
       <img alt="Humans fragile yet divine beings on Earth" src="https://storage.googleapis.com/a1aa/image/886eff4d-739f-46f3-73d9-17f664fb0a57.jpg"/>
       <span>Humans</span>
      </div>
     </div>
     <p style="margin-top: 2rem; font-size: 1.3rem;">
      The eternal cosmic flow of creation: <strong>Nirakar Shiva → Aakar Shiva → Shakti → Vishnu → Brahma → Farishtas → Devtas → Humans</strong>
     </p>
    </article>
   </section>
  </main>

  <!-- Fullscreen Button -->
  <button class="fullscreen-btn" id="fullscreenBtn" title="Toggle Fullscreen">
    <i class="fas fa-expand"></i>
  </button>

  <!-- Progress Bar -->
  <div class="progress-bar" id="progressBar"></div>

  <!-- Slide Counter -->
  <div class="slide-counter" id="slideCounter">1 / 12</div>

  <!-- Sacred Geometry and Symbols Background -->
  <img alt="Background of sacred geometry patterns glowing softly in violet and gold, including lotus flowers, trishul, conch shells, spiral galaxies, third eye symbols, OM signs, and yantras" aria-hidden="true" class="fixed inset-0 w-full h-full object-cover opacity-20 pointer-events-none -z-10" height="1080" loading="eager" src="https://storage.googleapis.com/a1aa/image/ce4a9533-3ae7-4048-5ff5-b51d3839d59b.jpg" width="1920"/>

  <!-- Ambient celestial music embed (muted by default) -->
  <audio autoplay="" class="hidden" id="ambient-music" loop="" muted="">
   <source src="https://cdn.pixabay.com/download/audio/2022/03/15/audio_0a7a3a3a3a.mp3?filename=ambient-celestial-11207.mp3" type="audio/mpeg"/>
  </audio>
  <script>
    // Enhanced Presentation System
    class CosmicPresentation {
      constructor() {
        this.slides = document.querySelectorAll(".slide");
        this.devtasSlides = document.querySelectorAll(".slide-devtas");
        this.currentSlide = 0;
        this.devtasCurrent = 0;
        this.isPlaying = true;
        this.slideDuration = 8000; // 8 seconds per slide
        this.devtasDuration = 4000; // 4 seconds per devta
        this.slideInterval = null;
        this.devtasInterval = null;
        this.progressInterval = null;
        this.slideStartTime = Date.now();

        this.initializeElements();
        this.setupEventListeners();
        this.setupCustomCursor();
        this.startPresentation();
      }

      initializeElements() {
        this.fullscreenBtn = document.getElementById("fullscreenBtn");
        this.progressBar = document.getElementById("progressBar");
        this.slideCounter = document.getElementById("slideCounter");
        this.customCursor = document.getElementById("customCursor");
        this.audio = document.getElementById("ambient-music");
      }

      setupEventListeners() {
        // Fullscreen button
        this.fullscreenBtn.addEventListener("click", () => this.toggleFullscreen());

        // Keyboard controls
        document.addEventListener("keydown", (e) => this.handleKeyboard(e));

        // Audio unmute on first interaction
        document.body.addEventListener("click", () => this.enableAudio(), { once: true });

        // Mouse movement for custom cursor
        document.addEventListener("mousemove", (e) => this.updateCursor(e));
      }

      setupCustomCursor() {
        document.addEventListener("mousemove", (e) => {
          this.customCursor.style.left = e.clientX + "px";
          this.customCursor.style.top = e.clientY + "px";
        });
      }

      updateCursor(e) {
        this.customCursor.style.left = e.clientX + "px";
        this.customCursor.style.top = e.clientY + "px";
      }

      enableAudio() {
        if (this.audio && this.audio.muted) {
          this.audio.muted = false;
          this.audio.volume = 0.15;
        }
      }

      startPresentation() {
        this.showSlide(this.currentSlide);
        this.showDevtasSlide(this.devtasCurrent);
        this.updateUI();

        if (this.isPlaying) {
          this.startIntervals();
        }
      }

      startIntervals() {
        this.slideStartTime = Date.now();

        // Main slide interval
        this.slideInterval = setInterval(() => {
          if (this.isPlaying) {
            this.nextSlide();
          }
        }, this.slideDuration);

        // Devtas slideshow interval
        this.devtasInterval = setInterval(() => {
          if (this.isPlaying) {
            this.nextDevtasSlide();
          }
        }, this.devtasDuration);

        // Progress bar update
        this.progressInterval = setInterval(() => {
          if (this.isPlaying) {
            this.updateProgress();
          }
        }, 100);
      }

      stopIntervals() {
        clearInterval(this.slideInterval);
        clearInterval(this.devtasInterval);
        clearInterval(this.progressInterval);
      }

      showSlide(index) {
        // Add exit animation to current slide
        this.slides.forEach((slide, i) => {
          slide.classList.remove("active", "exiting");
          if (i === this.currentSlide && i !== index) {
            slide.classList.add("exiting");
          }
        });

        // Show new slide after a brief delay
        setTimeout(() => {
          this.slides.forEach((slide, i) => {
            slide.classList.toggle("active", i === index);
            slide.classList.remove("exiting");
          });
        }, 200);

        this.currentSlide = index;
        this.slideStartTime = Date.now();
        this.updateUI();
      }

      showDevtasSlide(index) {
        this.devtasSlides.forEach((slide, i) => {
          slide.style.opacity = i === index ? "1" : "0";
          slide.style.position = i === index ? "relative" : "absolute";
          slide.style.pointerEvents = i === index ? "auto" : "none";
          slide.style.transition = "opacity 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)";
        });
        this.devtasCurrent = index;
      }

      nextSlide() {
        const nextIndex = (this.currentSlide + 1) % this.slides.length;
        this.showSlide(nextIndex);
      }

      previousSlide() {
        const prevIndex = this.currentSlide === 0 ? this.slides.length - 1 : this.currentSlide - 1;
        this.showSlide(prevIndex);
      }

      nextDevtasSlide() {
        this.devtasCurrent = (this.devtasCurrent + 1) % this.devtasSlides.length;
        this.showDevtasSlide(this.devtasCurrent);
      }

      togglePlayPause() {
        this.isPlaying = !this.isPlaying;

        if (this.isPlaying) {
          this.startIntervals();
        } else {
          this.stopIntervals();
        }
      }

      restartPresentation() {
        this.stopIntervals();
        this.currentSlide = 0;
        this.devtasCurrent = 0;
        this.showSlide(0);
        this.showDevtasSlide(0);

        if (this.isPlaying) {
          this.startIntervals();
        }
      }

      toggleFullscreen() {
        if (!document.fullscreenElement) {
          document.documentElement.requestFullscreen();
          this.fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
        } else {
          document.exitFullscreen();
          this.fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
        }
      }

      handleKeyboard(e) {
        switch(e.key) {
          case " ":
          case "Enter":
            e.preventDefault();
            this.togglePlayPause();
            break;
          case "ArrowRight":
          case "ArrowDown":
            e.preventDefault();
            this.nextSlide();
            break;
          case "ArrowLeft":
          case "ArrowUp":
            e.preventDefault();
            this.previousSlide();
            break;
          case "Home":
            e.preventDefault();
            this.restartPresentation();
            break;
          case "f":
          case "F11":
            e.preventDefault();
            this.toggleFullscreen();
            break;
          case "Escape":
            if (document.fullscreenElement) {
              document.exitFullscreen();
            }
            break;
        }
      }

      updateProgress() {
        const elapsed = Date.now() - this.slideStartTime;
        const progress = Math.min((elapsed / this.slideDuration) * 100, 100);
        const totalProgress = ((this.currentSlide + (progress / 100)) / this.slides.length) * 100;
        this.progressBar.style.width = totalProgress + "%";
      }

      updateUI() {
        this.slideCounter.textContent = `${this.currentSlide + 1} / ${this.slides.length}`;
      }
    }

    // Initialize presentation when DOM is loaded
    document.addEventListener("DOMContentLoaded", () => {
      new CosmicPresentation();
    });

    // Fallback initialization
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => new CosmicPresentation());
    } else {
      new CosmicPresentation();
    }
  </script>
 </body>
</html>
